namespace ExampleFramework.Tooling;

public class ExampleClass : Example
{
    bool isAutoGenerated = false;

    public ExampleClass(string classFullName, string? displayName) : base(classFullName, displayName)
    {
    }

    public ExampleClass(string classFullName, bool isAutoGenerated) : base(classFullName, null)
    {
        this.isAutoGenerated = isAutoGenerated;
    }

    public override bool IsAutoGenerated => this.isAutoGenerated;
}
