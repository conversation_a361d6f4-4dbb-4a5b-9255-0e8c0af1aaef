using System;
using Microsoft.Extensions.DependencyInjection;

namespace ExampleFramework.App;

public class ExampleClassReflection : ExampleReflection
{
    private readonly bool _isAutoGenerated;

    public ExampleClassReflection(ExampleAttribute exampleAttribute, Type type) : base(exampleAttribute)
    {
        Type = type;
    }

    public ExampleClassReflection(Type type, bool isAutoGenerated) : base(type)
    {
        Type = type;
        _isAutoGenerated = isAutoGenerated;
    }

    public Type Type { get; }

    public override object Create()
    {
        IServiceProvider? serviceProvider = ExampleApplication.GetInstance().ServiceProvider;
        if (serviceProvider is not null)
        {
            return ActivatorUtilities.CreateInstance(serviceProvider, Type);
        }
        else
        {
            return Activator.CreateInstance(Type);
        }
    }

    public override Type? DefaultUIComponentType => null;

    public override string Name => Type.FullName;

    public override bool IsAutoGenerated => _isAutoGenerated;
}
