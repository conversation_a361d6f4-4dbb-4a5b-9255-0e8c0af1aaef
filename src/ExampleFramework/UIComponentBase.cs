using System;
using System.Collections.Generic;
using System.Linq;

namespace ExampleFramework;

public abstract class UIComponentBase<TExample> where TExample : ExampleBase
{
    private readonly string? _displayName;
    private readonly List<TExample> _examples = [];

    public UIComponentBase(UIComponentKind kind, string? displayName)
    {
        _displayName = displayName;
        Kind = kind;
    }

    /// <summary>
    /// Name is intended to be what's used by the code to identify the component. It's the component's
    /// full qualified type name and is unique.
    /// </summary>
    public abstract string Name { get; }

    public UIComponentCategory? Category { get; private set; }

    public UIComponentKind Kind { get; }

    /// <summary>
    /// DisplayName is intended to be what's shown in the UI to identify the component. It can contain spaces and
    /// isn't necessarily unique. It defaults to the class name (with no namespace qualifier) but can be
    /// overridden by the developer.
    /// </summary>
    public string DisplayName => _displayName ?? NameUtilities.GetUnqualifiedName(Name);

    public bool HasExample => _examples.Count >= 0;

    public bool HasNoExamples => _examples.Count == 0;

    public bool HasSingleExample => _examples.Count == 1;

    public bool HasMultipleExamples => _examples.Count > 1;

    public void InitCategory(UIComponentCategory category)
    {
        if (Category != null && string.Compare(this.Category.Name, category.Name, StringComparison.OrdinalIgnoreCase) != 0)
        {
            throw new InvalidOperationException($"Component '{Name}' can't be set to category '{category.Name}' since it already has category '{this.Category.Name}' set");
        }
        Category = category;
    }

    public IReadOnlyList<TExample> Examples => _examples;

    public TExample? GetExample(string name)
    {
        foreach (TExample example in _examples)
        {
            if (example.Name.Equals(name, StringComparison.Ordinal))
            {
                return example;
            }
        }

        return null;
    }

    public TExample DefaultExample
    {
        get
        {
            if (_examples.Count == 0)
            {
                throw new InvalidOperationException($"Component '{Name}' has no examples");
            }

            // Currently, the default example is always the first one, though we may allow
            // it to be set explicitly in the future
            return _examples[0];
        }
    }

    public void AddExample(TExample example)
    {
        _examples.Add(example);

        // If there's a user defined example, remove any auto-generated examples
        if (!example.IsAutoGenerated)
        {
            RemoveAutoGeneratedExamples();
        }
    }

    public bool IsAutoGenerated => _examples.All(e => e.IsAutoGenerated);

    private void RemoveAutoGeneratedExamples()
    {
        _examples.RemoveAll(e => e.IsAutoGenerated);
    }
}
