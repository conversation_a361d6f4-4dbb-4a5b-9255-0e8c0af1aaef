<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:ExampleFramework.Maui.ViewModels"
             xmlns:pages="clr-namespace:ExampleFramework.Maui.Pages"
             x:Class="ExampleFramework.Maui.Pages.ExamplesPage"
             x:DataType="local:ExamplesViewModel"
             Shell.PresentationMode="NotAnimated"
             Title="UI Examples">
    
    <ContentPage.Resources>
        <DataTemplate x:Key="uiComponentTemplate" x:DataType="local:UIComponentViewModel">
            <HorizontalStackLayout Margin="0,4,0,0">
                <Image WidthRequest="20" Source="{Binding Icon}" />
                <Label Text="{Binding DisplayName}" Margin="4,0,0,0">
                    <Label.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding TapCommand}" />
                    </Label.GestureRecognizers>
                </Label>
            </HorizontalStackLayout>
        </DataTemplate>
        <DataTemplate x:Key="exampleTemplate" x:DataType="local:ExampleViewModel">
            <Label Text="{Binding DisplayName}" Margin="30,2,0,0">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding TapCommand}" />
                </Label.GestureRecognizers>
            </Label>
        </DataTemplate>
        <pages:ExampleItemDataTemplateSelector x:Key="exampleItemDataTemplateSelector"
            UIComponentTemplate="{StaticResource uiComponentTemplate}"
            ExampleTemplate="{StaticResource exampleTemplate}" />
    </ContentPage.Resources>

    <ScrollView Orientation="Vertical">
        <VerticalStackLayout>
            <Label
                Text="Examples"
                VerticalOptions="Center"
                HorizontalOptions="Center"
                FontSize="20" Margin="0,0,0,-5"/>
            <CollectionView Margin="10,5,0,0" 
                ItemsSource="{Binding ExamplesItems}"
                ItemTemplate="{StaticResource exampleItemDataTemplateSelector}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
