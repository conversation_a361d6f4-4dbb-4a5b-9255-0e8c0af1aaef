<Application x:Class="ExampleFramework.DevTools.App"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

  <Application.Resources>
    <ResourceDictionary>
      <ResourceDictionary.MergedDictionaries>
        <!-- Load WinUI resources -->
        <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
        <!-- Load Uno.UI.Toolkit resources -->
        <ToolkitResources xmlns="using:Uno.Toolkit.UI" />
      </ResourceDictionary.MergedDictionaries>

      <!-- Add resources here -->

    </ResourceDictionary>
  </Application.Resources>

</Application>
