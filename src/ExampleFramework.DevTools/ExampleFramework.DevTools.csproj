<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net9.0-desktop</TargetFrameworks>

    <OutputType>Exe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>ExampleFramework.DevTools</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.ExampleFramework.DevTools</ApplicationId>
    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <!-- Package Publisher -->
    <ApplicationPublisher>bret</ApplicationPublisher>
    <!-- Package Description -->
    <Description>ExampleFramework.DevTools powered by Uno Platform.</Description>

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Lottie;
      Hosting;
      Toolkit;
      Logging;
      Mvvm;
      Configuration;
      Navigation;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
  </PropertyGroup>

  <ItemGroup>
    <UnoIcon Include="Assets\Icons\icon.svg" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ExampleFramework.Tooling\ExampleFramework.Tooling.csproj" />
  </ItemGroup>

</Project>
