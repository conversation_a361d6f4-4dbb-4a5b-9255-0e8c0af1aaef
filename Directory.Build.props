<?xml version="1.0" encoding="utf-8"?>
<Project>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <RepoRootPath>$(MSBuildThisFileDirectory)</RepoRootPath>
    <BaseIntermediateOutputPath>$(RepoRootPath)obj\$([MSBuild]::MakeRelative($(RepoRootPath), $(MSBuildProjectDirectory)))\</BaseIntermediateOutputPath>
    <BaseOutputPath Condition=" '$(BaseOutputPath)' == '' ">$(RepoRootPath)bin\$(MSBuildProjectName)\</BaseOutputPath>
    <PackageOutputPath>$(RepoRootPath)bin\Packages\$(Configuration)\</PackageOutputPath>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AnalysisLevel>latest</AnalysisLevel>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
    <!-- <GenerateDocumentationFile>true</GenerateDocumentationFile> -->
    <ProduceReferenceAssembly>true</ProduceReferenceAssembly>

    <!-- https://github.com/dotnet/msbuild/blob/main/documentation/ProjectReference-Protocol.md#setplatform-negotiation -->
    <EnableDynamicPlatformResolution>true</EnableDynamicPlatformResolution>

    <!-- Opt in till https://github.com/NuGet/Home/issues/9803 makes this the default. -->
    <!-- Opt back out until an SDK with the fix for https://github.com/NuGet/Home/issues/12177 is generally available. -->
    <RestoreUseStaticGraphEvaluation>false</RestoreUseStaticGraphEvaluation>

    <!-- This entire repo has just one version.json file, so compute the version once and share with all projects in a large build. -->
    <GitVersionBaseDirectory>$(MSBuildThisFileDirectory)</GitVersionBaseDirectory>

    <!-- Local builds should embed PDBs so we never lose them when a subsequent build occurs. -->
    <DebugType Condition=" '$(CI)' != 'true' and '$(TF_BUILD)' != 'true' ">embedded</DebugType>

    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>$(MSBuildThisFileDirectory)strongname.snk</AssemblyOriginatorKeyFile>

    <PackageProjectUrl>https://github.com/BretJohnson/preview-framework</PackageProjectUrl>
    <Company>Bret Johnson</Company>
    <Authors>Bret Johnson</Authors>
    <Copyright>© Bret Johnson. All rights reserved.</Copyright>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <IncludeSymbols Condition=" '$(DebugType)' != 'embedded' ">true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <PropertyGroup>
    <LangVersion Condition="'$(MSBuildProjectExtension)'=='.csproj'">13</LangVersion>
    <LangVersion Condition="'$(MSBuildProjectExtension)'=='.vbproj'">16.9</LangVersion>
  </PropertyGroup>

  <ItemDefinitionGroup>
    <!-- We always want MSBuild properties generated that point at the restored location of each package. -->
    <PackageReference GeneratePathProperty="true" />
  </ItemDefinitionGroup>

  <Target Name="PrepareReleaseNotes" BeforeTargets="GenerateNuspec" DependsOnTargets="GetBuildVersion">
    <PropertyGroup>
      <PackageReleaseNotes Condition="'$(RepositoryUrl)'!=''">$(RepositoryUrl)/releases/tag/v$(Version)</PackageReleaseNotes>
    </PropertyGroup>
  </Target>
</Project>
