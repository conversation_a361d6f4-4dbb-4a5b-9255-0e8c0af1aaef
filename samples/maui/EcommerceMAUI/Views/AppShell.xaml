<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="EcommerceMAUI.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views="clr-namespace:EcommerceMAUI.Views"
    Shell.FlyoutBehavior="Disabled">
    <TabBar>
        <Tab Title="Explore">
            <Tab.Icon>
                <FontImageSource FontFamily="MaterialIcon" Glyph="&#xf56e;" />
            </Tab.Icon>
            <ShellContent ContentTemplate="{DataTemplate views:HomePageView}" Route="HomePageView" />
        </Tab>
        <Tab Title="Cart">
            <Tab.Icon>
                <FontImageSource FontFamily="MaterialIcon" Glyph="&#xf110;" />
            </Tab.Icon>
            <ShellContent ContentTemplate="{DataTemplate views:CartView}" Route="CartView" />
        </Tab>
        <Tab Title="Account">
            <Tab.Icon>
                <FontImageSource FontFamily="MaterialIcon" Glyph="&#xf004;" />
            </Tab.Icon>
            <ShellContent ContentTemplate="{DataTemplate views:ProfileView}" Route="ProfileView" />
        </Tab>
    </TabBar>

</Shell>
