<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="EcommerceMAUI.Views.WishListView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:EcommerceMAUI.Converters"
    x:Name="WishPage"
    Title="Wish List">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid Margin="16">
        <CollectionView IsVisible="{Binding IsLoaded}" ItemsSource="{Binding Products}">
            <CollectionView.ItemsLayout>
                <GridItemsLayout Orientation="Vertical" VerticalItemSpacing="16" />
            </CollectionView.ItemsLayout>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <StackLayout Orientation="Horizontal" Spacing="16">
                        <Image
                            Aspect="AspectFit"
                            HeightRequest="120"
                            Source="{Binding ImageUrl}"
                            WidthRequest="120" />
                        <StackLayout Spacing="6">
                            <Label
                                FontSize="16"
                                HorizontalOptions="StartAndExpand"
                                Text="{Binding Name}"
                                TextColor="Black" />
                            <Label
                                FontSize="16"
                                HorizontalOptions="StartAndExpand"
                                Text="{Binding Price}"
                                TextColor="{StaticResource Primary}" />
                            <Button
                                Margin="0,20,0,0"
                                Padding="32,12"
                                BackgroundColor="{Binding AvailableColor}"
                                Command="{Binding Source={x:Reference WishPage}, Path=BindingContext.SelectProductCommand}"
                                CommandParameter="{Binding .}"
                                CornerRadius="4"
                                HorizontalOptions="Start"
                                Text="{Binding AvailableText}"
                                TextColor="White" />
                        </StackLayout>
                    </StackLayout>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <ActivityIndicator
            HeightRequest="45"
            HorizontalOptions="Center"
            IsRunning="{Binding IsLoaded, Converter={StaticResource InverseBoolConverter}}"
            VerticalOptions="Center"
            WidthRequest="45"
            Color="{StaticResource Primary}" />
    </Grid>
</ContentPage>