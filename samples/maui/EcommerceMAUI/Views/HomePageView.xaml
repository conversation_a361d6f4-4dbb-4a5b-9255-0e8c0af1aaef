<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="EcommerceMAUI.Views.HomePageView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:EcommerceMAUI.Converters"
    xmlns:vm="clr-namespace:EcommerceMAUI.ViewModel"
    x:Name="HomePage"
    Title="Home Page"
    Shell.NavBarIsVisible="False">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid Margin="8,12" RowDefinitions="Auto,*">
        <StackLayout
            Grid.Row="0"
            Margin="0"
            HorizontalOptions="FillAndExpand"
            IsVisible="{Binding IsLoaded}"
            Orientation="Horizontal">
            <Border
                Margin="0"
                Padding="0"
                Background="Transparent"
                HorizontalOptions="FillAndExpand"
                StrokeShape="RoundRectangle 22"
                StrokeThickness="0">
                <StackLayout
                    Margin="12,2"
                    Orientation="Horizontal"
                    VerticalOptions="CenterAndExpand">
                    <Label
                        FontFamily="MaterialIcon"
                        FontSize="20"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="&#xf349;"
                        TextColor="Black"
                        VerticalOptions="CenterAndExpand" />

                    <Entry
                        Margin="8,0"
                        FontSize="12"
                        HorizontalOptions="FillAndExpand"
                        Placeholder="Search Here" />
                </StackLayout>
            </Border>

            <Border
                Margin="6,0"
                Padding="0"
                Background="{StaticResource Primary}"
                HeightRequest="40"
                HorizontalOptions="End"
                StrokeShape="RoundRectangle 20"
                StrokeThickness="0"
                WidthRequest="40">
                <Label
                    FontFamily="MaterialIcon"
                    FontSize="22"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    Text="&#xf100;"
                    TextColor="White"
                    VerticalOptions="CenterAndExpand" />
                <Border.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding OpenCameraCommand}" />
                </Border.GestureRecognizers>
            </Border>
        </StackLayout>
        <Grid Grid.Row="1" VerticalOptions="CenterAndExpand">
            <Grid IsVisible="{Binding IsLoaded}">
                <ScrollView VerticalOptions="FillAndExpand">
                    <StackLayout Spacing="0" VerticalOptions="Start">
                        <Label
                            Margin="0,12"
                            FontAttributes="Bold"
                            FontSize="16"
                            HorizontalOptions="Start"
                            HorizontalTextAlignment="Start"
                            Text="Categories"
                            TextColor="Black"
                            VerticalOptions="EndAndExpand" />

                        <CollectionView
                            Margin="0,6"
                            ItemsSource="{Binding Categories}"
                            SelectionMode="Single"
                            VerticalScrollBarVisibility="Never">
                            <CollectionView.ItemsLayout>
                                <GridItemsLayout
                                    HorizontalItemSpacing="12"
                                    Orientation="Vertical"
                                    Span="5"
                                    VerticalItemSpacing="0" />
                            </CollectionView.ItemsLayout>

                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <StackLayout Spacing="0">
                                        <Border
                                            Margin="0,4"
                                            Padding="12"
                                            Background="White"
                                            HeightRequest="60"
                                            HorizontalOptions="Center"
                                            Stroke="Gray"
                                            StrokeShape="RoundRectangle 30"
                                            VerticalOptions="Center"
                                            WidthRequest="60">
                                            <Label
                                                FontFamily="MaterialIcon"
                                                FontSize="24"
                                                HorizontalOptions="Center"
                                                HorizontalTextAlignment="Center"
                                                Text="{Binding Icon}"
                                                TextColor="Black"
                                                VerticalOptions="Center"
                                                VerticalTextAlignment="Center" />
                                        </Border>

                                        <Label
                                            FontSize="14"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Text="{Binding CategoryName}"
                                            TextColor="Black"
                                            VerticalOptions="EndAndExpand" />
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference HomePage}, Path=BindingContext.CategoryTapCommand}" CommandParameter="{Binding .}" />
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <StackLayout
                            Margin="0,12"
                            Orientation="Horizontal"
                            Spacing="0">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                HorizontalOptions="Start"
                                HorizontalTextAlignment="Start"
                                Text="Best Selling"
                                TextColor="Black"
                                VerticalOptions="Center" />

                            <Button
                                BackgroundColor="Transparent"
                                Command="{Binding RecommendedTapCommand}"
                                FontSize="16"
                                HorizontalOptions="EndAndExpand"
                                Text="View All"
                                TextColor="Black"
                                VerticalOptions="Center" />
                        </StackLayout>

                        <CollectionView Margin="0,12" ItemsSource="{Binding BestSellingProducts}">

                            <CollectionView.ItemsLayout>
                                <LinearItemsLayout ItemSpacing="12" Orientation="Horizontal" />
                            </CollectionView.ItemsLayout>

                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <StackLayout
                                        Margin="0"
                                        Background="White"
                                        HorizontalOptions="FillAndExpand">
                                        <Border
                                            Padding="0"
                                            Background="transparent"
                                            StrokeShape="RoundRectangle 6,0,0,6"
                                            StrokeThickness="1">
                                            <Image
                                                Aspect="AspectFit"
                                                HeightRequest="240"
                                                Source="{Binding ImageUrl}"
                                                WidthRequest="165" />
                                        </Border>

                                        <StackLayout Margin="0,8,0,0">
                                            <Label
                                                FontSize="16"
                                                HorizontalOptions="Center"
                                                Text="{Binding Name}"
                                                TextColor="Black"
                                                VerticalOptions="Start" />

                                            <Label
                                                FontSize="12"
                                                HorizontalOptions="Center"
                                                Text="{Binding BrandName}"
                                                TextColor="{StaticResource SecondaryTextColor}"
                                                VerticalOptions="Start" />

                                            <Label
                                                FontSize="16"
                                                HorizontalOptions="Center"
                                                Text="{Binding Price, StringFormat='${0:F2}'}"
                                                TextColor="{StaticResource Primary}"
                                                VerticalOptions="Start" />
                                        </StackLayout>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference HomePage}, Path=BindingContext.SelectProductCommand}" CommandParameter="{Binding .}" />
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <Label
                            Margin="0,12"
                            FontAttributes="Bold"
                            FontSize="16"
                            HorizontalOptions="Start"
                            HorizontalTextAlignment="Start"
                            Text="Featured Brands"
                            TextColor="Black"
                            VerticalOptions="EndAndExpand" />

                        <CollectionView Margin="0,10" ItemsSource="{Binding FeaturedBrands}">
                            <CollectionView.ItemsLayout>
                                <LinearItemsLayout ItemSpacing="12" Orientation="Horizontal" />
                            </CollectionView.ItemsLayout>
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <StackLayout>
                                        <Border
                                            Padding="16,8"
                                            Background="white"
                                            HorizontalOptions="CenterAndExpand"
                                            Stroke="wheat"
                                            StrokeShape="RoundRectangle 6"
                                            VerticalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal">
                                                <Frame
                                                    Padding="0"
                                                    CornerRadius="20"
                                                    HasShadow="False"
                                                    HeightRequest="40"
                                                    Opacity="10"
                                                    WidthRequest="40">
                                                    <Image
                                                        Aspect="AspectFit"
                                                        HeightRequest="40"
                                                        HorizontalOptions="CenterAndExpand"
                                                        Source="{Binding ImageUrl}"
                                                        VerticalOptions="CenterAndExpand"
                                                        WidthRequest="40" />
                                                </Frame>
                                                <StackLayout Margin="6,0">
                                                    <Label
                                                        FontAttributes="Bold"
                                                        FontSize="16"
                                                        HorizontalOptions="Center"
                                                        Text="{Binding BrandName}"
                                                        TextColor="Black" />
                                                    <Label
                                                        FontSize="12"
                                                        HorizontalOptions="Center"
                                                        Text="{Binding Details}"
                                                        TextColor="{StaticResource SecondaryTextColor}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <Border.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference HomePage}, Path=BindingContext.BrandTapCommand}" CommandParameter="{Binding .}" />
                                            </Border.GestureRecognizers>
                                        </Border>
                                    </StackLayout>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <StackLayout
                            Margin="0,12"
                            Orientation="Horizontal"
                            Spacing="0">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                HorizontalOptions="Start"
                                HorizontalTextAlignment="Start"
                                Text="Best Selling"
                                TextColor="Black"
                                VerticalOptions="Center" />
                            <Button
                                BackgroundColor="Transparent"
                                Command="{Binding RecommendedTapCommand}"
                                FontSize="16"
                                HorizontalOptions="EndAndExpand"
                                Text="View All"
                                TextColor="Black"
                                VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView Margin="12" ItemsSource="{Binding BestSellingProducts}">
                            <CollectionView.ItemsLayout>
                                <GridItemsLayout
                                    HorizontalItemSpacing="12"
                                    Orientation="Vertical"
                                    Span="2"
                                    VerticalItemSpacing="12" />
                            </CollectionView.ItemsLayout>
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <StackLayout
                                        Margin="0"
                                        Background="White"
                                        HorizontalOptions="FillAndExpand">
                                        <Border
                                            Background="transparent"
                                            StrokeShape="RoundRectangle 6,0,0,6"
                                            StrokeThickness="1">
                                            <Image
                                                Aspect="AspectFit"
                                                HeightRequest="240"
                                                Source="{Binding ImageUrl}"
                                                WidthRequest="165" />
                                        </Border>
                                        <StackLayout Margin="0,8,0,0">
                                            <Label
                                                FontSize="16"
                                                HorizontalOptions="Center"
                                                Text="{Binding Name}"
                                                TextColor="Black"
                                                VerticalOptions="Start" />
                                            <Label
                                                FontSize="12"
                                                HorizontalOptions="Center"
                                                Text="{Binding BrandName}"
                                                TextColor="{StaticResource SecondaryTextColor}"
                                                VerticalOptions="Start" />
                                            <Label
                                                FontSize="16"
                                                HorizontalOptions="Center"
                                                Text="{Binding Price, StringFormat='${0:F2}'}"
                                                TextColor="{StaticResource Primary}"
                                                VerticalOptions="Start" />
                                        </StackLayout>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference HomePage}, Path=BindingContext.SelectProductCommand}" CommandParameter="{Binding .}" />
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </StackLayout>
                </ScrollView>
            </Grid>
            <ActivityIndicator
                HeightRequest="45"
                HorizontalOptions="Center"
                IsRunning="{Binding IsLoaded, Converter={StaticResource InverseBoolConverter}}"
                VerticalOptions="Center"
                WidthRequest="45"
                Color="{StaticResource Primary}" />
        </Grid>
    </Grid>
</ContentPage>