<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="EcommerceMAUI.Views.CardView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:EcommerceMAUI.Converters"
    xmlns:views="clr-namespace:EcommerceMAUI.Controls"
    Title="Cards">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid RowDefinitions="*,Auto">
        <CollectionView
            x:Name="MyCollectionView"
            Margin="16"
            IsVisible="{Binding IsLoaded}"
            ItemsSource="{Binding Cards}">
            <CollectionView.ItemsLayout>
                <GridItemsLayout Orientation="Vertical" VerticalItemSpacing="16" />
            </CollectionView.ItemsLayout>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid>
                        <views:CreditCardView
                            CardNumber="{Binding CardNumber}"
                            CardValidationCode="{Binding CardValidationCode}"
                            ExpirationDate="{Binding ExpirationDate}"
                            HorizontalOptions="FillAndExpand" />
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <ActivityIndicator
            HeightRequest="45"
            HorizontalOptions="Center"
            IsRunning="{Binding IsLoaded, Converter={StaticResource InverseBoolConverter}}"
            VerticalOptions="Center"
            WidthRequest="45"
            Color="{StaticResource Primary}" />

        <Grid
            Grid.Row="1"
            Margin="16,0,16,16"
            ColumnDefinitions="*,24,*">
            <Button
                Grid.Column="2"
                Padding="16"
                Background="{DynamicResource Primary}"
                Command="{Binding AddNewCommand}"
                CornerRadius="4"
                IsVisible="{Binding IsLoaded}"
                Text="NEW"
                TextColor="{DynamicResource White}" />
        </Grid>

    </Grid>
</ContentPage>