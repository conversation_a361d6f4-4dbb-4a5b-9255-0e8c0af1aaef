<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="EcommerceMAUI.Views.ConfirmPaymentView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:EcommerceMAUI.Converters"
    x:Name="ConfirmPaymentPage"
    Title="Select Payment">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:InverseBooleanConverter x:Key="InverseBoolConverter" />
        </ResourceDictionary>
        <Style x:Key="CreditCardImageLabelStyle" TargetType="Label">
            <Setter Property="FontSize" Value="48" />
            <Setter Property="TextColor" Value="#FFFFFF" />
            <Setter Property="HorizontalOptions" Value="EndAndExpand" />
        </Style>
    </ContentPage.Resources>
    <Grid RowDefinitions="*,Auto">
        <ScrollView>
            <StackLayout Margin="16">
                <Grid Margin="8,42" RowDefinitions="Auto,12,Auto">
                    <Grid ColumnDefinitions="8,Auto,*,Auto,*,Auto,8">
                        <Border
                            Grid.Column="1"
                            Padding="4"
                            Background="Transparent"
                            HeightRequest="32"
                            HorizontalOptions="FillAndExpand"
                            Stroke="{StaticResource Gray200}"
                            StrokeShape="RoundRectangle 16"
                            StrokeThickness="1"
                            WidthRequest="32">
                            <Border
                                Padding="4"
                                Background="{StaticResource Primary}"
                                HeightRequest="16"
                                Stroke="{StaticResource Primary}"
                                StrokeShape="RoundRectangle 16"
                                StrokeThickness="1"
                                WidthRequest="16" />
                        </Border>

                        <Border
                            Grid.Column="2"
                            Background="{StaticResource Primary}"
                            HeightRequest="2"
                            HorizontalOptions="FillAndExpand"
                            Stroke="{StaticResource Primary}"
                            StrokeShape="RoundRectangle 16"
                            StrokeThickness="1"
                            VerticalOptions="CenterAndExpand" />

                        <Border
                            Grid.Column="3"
                            Padding="4"
                            Background="Transparent"
                            HeightRequest="32"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="{StaticResource Gray200}"
                            StrokeShape="RoundRectangle 16"
                            StrokeThickness="1"
                            WidthRequest="32">
                            <Border
                                Padding="4"
                                Background="{StaticResource Primary}"
                                HeightRequest="16"
                                Stroke="{StaticResource Primary}"
                                StrokeShape="RoundRectangle 16"
                                StrokeThickness="1"
                                WidthRequest="16" />
                        </Border>

                        <Border
                            Grid.Column="4"
                            Background="{StaticResource Primary}"
                            HeightRequest="2"
                            HorizontalOptions="FillAndExpand"
                            Stroke="{StaticResource Primary}"
                            StrokeShape="RoundRectangle 16"
                            StrokeThickness="1"
                            VerticalOptions="CenterAndExpand" />

                        <Border
                            Grid.Column="5"
                            Padding="4"
                            Background="Transparent"
                            HeightRequest="32"
                            HorizontalOptions="CenterAndExpand"
                            Stroke="{StaticResource Primary}"
                            StrokeShape="RoundRectangle 16"
                            StrokeThickness="1"
                            WidthRequest="32">
                            <Border
                                Padding="4"
                                Background="{StaticResource Primary}"
                                HeightRequest="16"
                                Stroke="{StaticResource Primary}"
                                StrokeShape="RoundRectangle 16"
                                StrokeThickness="1"
                                WidthRequest="16" />
                        </Border>

                    </Grid>

                    <Grid
                        Grid.Row="2"
                        ColumnDefinitions="Auto,*,Auto,*,Auto"
                        HorizontalOptions="FillAndExpand">
                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            HorizontalOptions="Center"
                            Text="Delivery"
                            TextColor="Black" />

                        <Label
                            Grid.Column="2"
                            FontSize="14"
                            HorizontalOptions="Center"
                            Text="Address"
                            TextColor="Black" />

                        <Label
                            Grid.Column="4"
                            FontSize="14"
                            HorizontalOptions="Center"
                            Text="Payments"
                            TextColor="Black" />

                    </Grid>
                </Grid>

                <StackLayout
                    BindableLayout.ItemsSource="{Binding Cards}"
                    IsVisible="{Binding IsLoaded}"
                    Spacing="12">
                    <BindableLayout.ItemTemplate>
                        <DataTemplate>
                            <StackLayout
                                HorizontalOptions="FillAndExpand"
                                Orientation="Horizontal"
                                Spacing="16">
                                <Grid
                                    Padding="0,12"
                                    ColumnDefinitions="Auto,12,*,12,Auto"
                                    HorizontalOptions="FillAndExpand">
                                    <Border
                                        Grid.Column="0"
                                        Padding="0"
                                        Background="{Binding CardColor}"
                                        Stroke="{Binding CardColor}"
                                        StrokeShape="RoundRectangle 4">
                                        <Label
                                            FontFamily="{Binding FontFamily}"
                                            Style="{StaticResource CreditCardImageLabelStyle}"
                                            Text="{Binding Icon}" />
                                    </Border>
                                    <StackLayout Grid.Column="2" HorizontalOptions="FillAndExpand">
                                        <Label
                                            FontAttributes="Bold"
                                            FontSize="16"
                                            Text="{Binding MaskedCardNumber}"
                                            TextColor="Black" />

                                        <Label
                                            FontSize="14"
                                            HorizontalOptions="FillAndExpand"
                                            Text="{Binding CardType}"
                                            VerticalOptions="Center" />
                                    </StackLayout>

                                    <Label
                                        Grid.Column="4"
                                        FontFamily="MaterialIcon"
                                        FontSize="24"
                                        HorizontalOptions="EndAndExpand"
                                        IsVisible="{Binding IsSelected}"
                                        Text="&#xf133;"
                                        TextColor="{DynamicResource Primary}"
                                        VerticalOptions="Center" />

                                </Grid>
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference ConfirmPaymentPage}, Path=BindingContext.SelectPaymentCommand}" CommandParameter="{Binding .}" />
                                </StackLayout.GestureRecognizers>
                            </StackLayout>
                        </DataTemplate>
                    </BindableLayout.ItemTemplate>
                </StackLayout>
            </StackLayout>

        </ScrollView>

        <ActivityIndicator
            HeightRequest="45"
            HorizontalOptions="Center"
            IsRunning="{Binding IsLoaded, Converter={StaticResource InverseBoolConverter}}"
            VerticalOptions="Center"
            WidthRequest="45"
            Color="{StaticResource Primary}" />
        <Grid
            Grid.Row="1"
            Margin="16"
            ColumnDefinitions="*,24,*">
            <Button
                Grid.Column="0"
                Padding="16"
                Background="Transparent"
                BorderColor="{DynamicResource Primary}"
                Command="{Binding BackCommand}"
                CornerRadius="4"
                IsVisible="{Binding IsLoaded}"
                Text="BACK"
                TextColor="{DynamicResource Black}" />
            <Button
                Grid.Column="2"
                Padding="16"
                Background="{DynamicResource Primary}"
                Command="{Binding NextCommand}"
                CornerRadius="4"
                IsVisible="{Binding IsLoaded}"
                Text="NEXT"
                TextColor="{DynamicResource White}" />

        </Grid>
    </Grid>
</ContentPage>